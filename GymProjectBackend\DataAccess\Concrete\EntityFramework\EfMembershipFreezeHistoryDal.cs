﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMembershipFreezeHistoryDal : EfCompanyEntityRepositoryBase<MembershipFreezeHistory, GymContext>, IMembershipFreezeHistoryDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        public EfMembershipFreezeHistoryDal(GymContext context, Core.Utilities.Security.CompanyContext.ICompanyContext companyContext) : base(context, companyContext)
        {
            _companyContext = companyContext;
        }
        public List<MembershipFreezeHistoryDto> GetFreezeHistoryDetails()
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var result = from fh in context.MembershipFreezeHistory
                             join m in context.Memberships on fh.MembershipID equals m.MembershipID
                             join mem in context.Members on m.MemberID equals mem.MemberID
                             join mt in context.MembershipTypes on m.MembershipTypeID equals mt.MembershipTypeID
                             where fh.CompanyID == companyId // Şirket ID'sine göre filtrele
                             && m.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                             && mem.CompanyID == companyId // Üyelerin de aynı şirkete ait olduğundan emin ol
                             && mt.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                             orderby fh.CreationDate descending
                             select new MembershipFreezeHistoryDto
                             {
                                 FreezeHistoryID = fh.FreezeHistoryID,
                                 MemberName = mem.Name ?? "",
                                 PhoneNumber = mem.PhoneNumber ?? "",
                                 Branch = mt.Branch ?? "",
                                 StartDate = fh.StartDate,
                                 PlannedEndDate = fh.PlannedEndDate,
                                 ActualEndDate = fh.ActualEndDate,
                                 FreezeDays = fh.FreezeDays,
                                 UsedDays = fh.UsedDays,
                                 CancellationType = fh.CancellationType ?? "",
                                 CreationDate = fh.CreationDate
                             };

                return result.ToList();
            }
        }

        public List<MembershipFreezeHistoryDto> GetFreezeHistoryByMembershipId(int membershipId)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var result = from fh in context.MembershipFreezeHistory
                             join m in context.Memberships on fh.MembershipID equals m.MembershipID
                             join mem in context.Members on m.MemberID equals mem.MemberID
                             join mt in context.MembershipTypes on m.MembershipTypeID equals mt.MembershipTypeID
                             where fh.MembershipID == membershipId
                             && fh.CompanyID == companyId // Şirket ID'sine göre filtrele
                             && m.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                             && mem.CompanyID == companyId // Üyelerin de aynı şirkete ait olduğundan emin ol
                             && mt.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                             orderby fh.CreationDate descending
                             select new MembershipFreezeHistoryDto
                             {
                                 FreezeHistoryID = fh.FreezeHistoryID,
                                 MemberName = mem.Name ?? "",
                                 PhoneNumber = mem.PhoneNumber ?? "",
                                 Branch = mt.Branch ?? "",
                                 StartDate = fh.StartDate,
                                 PlannedEndDate = fh.PlannedEndDate,
                                 ActualEndDate = fh.ActualEndDate,
                                 FreezeDays = fh.FreezeDays,
                                 UsedDays = fh.UsedDays,
                                 CancellationType = fh.CancellationType ?? "",
                                 CreationDate = fh.CreationDate
                             };

                return result.ToList();
            }
        }

        public int GetTotalFreezeDaysUsedInLastYear(int membershipId)
        {
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            var oneYearAgo = DateTime.Now.AddYears(-1);
            return _context.MembershipFreezeHistory
                .Where(fh => fh.MembershipID == membershipId &&
                       fh.CreationDate >= oneYearAgo &&
                       fh.CompanyID == companyId) // Şirket ID'sine göre filtrele
                .Sum(fh => fh.UsedDays ?? fh.FreezeDays);
        }
    }
}