using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfCompanyDal : EfEntityRepositoryBase<Company, GymContext>, ICompanyDal
    {
        public EfCompanyDal(GymContext context) : base(context)
        {
        }

        public List<ActiveCompanyDetailDto> GetActiveCompanies()
        {
            using (GymContext context = new GymContext())
            {
                var result = from c in context.Companies
                             where c.IsActive == true
                             select new ActiveCompanyDetailDto
                             {
                               CompanyID = c.CompanyID,
                               CompanyName = c.CompanyName,
                               PhoneNumber = c.PhoneNumber,
                               IsActive = c.IsActive,
                              
                             };
                return result.ToList();
            }
        }
    }
}
